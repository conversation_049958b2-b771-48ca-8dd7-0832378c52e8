import 'package:flutter/material.dart';
import 'package:insight/view/add_new_branch_screen.dart';
import '../services/api_service.dart';
import 'package:insight/l10n/app_localizations.dart';

class ManageBranchScreen extends StatefulWidget {
  const ManageBranchScreen({super.key});

  @override
  State<ManageBranchScreen> createState() => _ManageBranchScreenState();
}

class _ManageBranchScreenState extends State<ManageBranchScreen> {
  final ApiService _apiService = ApiService();
  List<Map<String, dynamic>> _branches = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadBranches();
  }

  Future<void> _loadBranches() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await _apiService.getAllBranches();
      setState(() {
        _branches = List<Map<String, dynamic>>.from(response['branches'] ?? []);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString().replaceFirst('Exception: ', '');
        _isLoading = false;
      });
    }
  }

  Future<void> _navigateToAddBranch() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddNewBranchScreen()),
    );

    // If a branch was successfully added, refresh the list
    if (result == true) {
      _loadBranches();
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          localizations?.manageBranch ?? 'Manage Branch',
          style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: false,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(color: Colors.grey.shade200, height: 1.0),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: _isLoading
            ? const Center(
                child: CircularProgressIndicator(color: Color(0xFF209A9F)),
              )
            : _errorMessage != null
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Error: $_errorMessage',
                      style: const TextStyle(fontSize: 16, color: Colors.grey),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadBranches,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF209A9F),
                      ),
                      child: Text(
                        localizations?.retry ?? 'Retry',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                ),
              )
            : _branches.isEmpty
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.business_outlined,
                      size: 64,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      localizations?.noBranchesFound ?? 'No branches found',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      localizations?.addFirstBranch ??
                          'Add your first branch to get started',
                      style: TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                  ],
                ),
              )
            : RefreshIndicator(
                onRefresh: _loadBranches,
                color: const Color(0xFF209A9F),
                child: ListView.separated(
                  itemCount: _branches.length,
                  separatorBuilder: (context, index) =>
                      const SizedBox(height: 12),
                  itemBuilder: (context, index) {
                    final branch = _branches[index];
                    return BranchCard(
                      name:
                          branch['branchName'] ??
                          (localizations?.unknownBranch ?? 'Unknown'),
                      location:
                          branch['branchAddress'] ??
                          (localizations?.unknownAddress ?? 'Unknown Address'),
                    );
                  },
                ),
              ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(20.0),
        child: ElevatedButton.icon(
          onPressed: _navigateToAddBranch,
          icon: const Icon(Icons.add, color: Colors.white, size: 24.0),
          label: Text(
            localizations?.addNewBranch ?? 'Add New Branch',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 16.0,
            ),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF209A9F),
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            minimumSize: const Size(double.infinity, 50),
          ),
        ),
      ),
    );
  }
}

class BranchCard extends StatelessWidget {
  final String name;
  final String location;

  const BranchCard({super.key, required this.name, required this.location});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: const TextStyle(
                    fontSize: 16.0,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8.0),
                Text(
                  location,
                  style: const TextStyle(fontSize: 14.0, color: Colors.grey),
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {
              // TODO: Implement more options popup
            },
          ),
        ],
      ),
    );
  }
}
